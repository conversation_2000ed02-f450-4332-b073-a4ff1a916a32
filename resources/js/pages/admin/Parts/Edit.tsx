import { <PERSON>, <PERSON>, useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Save, Plus, X, Image as ImageIcon, Eye, Move } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import MediaPicker from '@/components/MediaPicker';
import { useState } from 'react';

interface Category {
    id: number;
    name: string;
    description: string | null;
    parent_id: number | null;
    is_active: boolean;
}

interface Media {
    id: number;
    filename: string;
    original_filename: string;
    mime_type: string;
    size: number;
    path: string;
    alt_text: string | null;
    title: string | null;
    description: string | null;
    width: number | null;
    height: number | null;
    url: string;
    formatted_size: string;
    created_at: string;
}

interface Part {
    id: number;
    category_id: number;
    name: string;
    part_number: string | null;
    manufacturer: string | null;
    description: string | null;
    specifications: Record<string, string> | null;
    images: string[] | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    category: Category;
}

interface Props {
    part: Part;
    categories: Category[];
}

export default function Edit({ part, categories }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        category_id: part.category_id.toString(),
        name: part.name,
        part_number: part.part_number || '',
        manufacturer: part.manufacturer || '',
        description: part.description || '',
        specifications: part.specifications || {},
        images: part.images || [],
        is_active: part.is_active,
    });

    const [newSpecKey, setNewSpecKey] = useState('');
    const [newSpecValue, setNewSpecValue] = useState('');
    const [isMediaPickerOpen, setIsMediaPickerOpen] = useState(false);
    const [selectedImages, setSelectedImages] = useState<Media[]>([]);
    const [previewImage, setPreviewImage] = useState<string | null>(null);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Flash messages from the backend will be handled by FlashMessageHandler
        put(`/admin/parts/${part.id}`);
    };

    const addSpecification = () => {
        if (newSpecKey && newSpecValue) {
            setData('specifications', {
                ...data.specifications,
                [newSpecKey]: newSpecValue
            });
            setNewSpecKey('');
            setNewSpecValue('');
        }
    };

    const removeSpecification = (key: string) => {
        const newSpecs = { ...data.specifications };
        delete newSpecs[key];
        setData('specifications', newSpecs);
    };

    const handleMediaSelect = (media: Media[]) => {
        const newImages = media.map(item => item.url);
        setData('images', [...data.images, ...newImages]);
        setSelectedImages([...selectedImages, ...media]);
        setIsMediaPickerOpen(false);
    };

    const removeImage = (index: number) => {
        const newImages = data.images.filter((_, i) => i !== index);
        const newSelectedImages = selectedImages.filter((_, i) => i !== index);
        setData('images', newImages);
        setSelectedImages(newSelectedImages);
    };

    const moveImage = (fromIndex: number, toIndex: number) => {
        const newImages = [...data.images];
        const newSelectedImages = [...selectedImages];

        // Move the image
        const [movedImage] = newImages.splice(fromIndex, 1);
        newImages.splice(toIndex, 0, movedImage);

        // Move the corresponding selected image
        const [movedSelectedImage] = newSelectedImages.splice(fromIndex, 1);
        newSelectedImages.splice(toIndex, 0, movedSelectedImage);

        setData('images', newImages);
        setSelectedImages(newSelectedImages);
    };

    return (
        <AppLayout>
            <Head title={`Edit ${part.name} - Parts - Admin`} />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Link href="/admin/parts">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Parts
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Edit Part</h1>
                        <p className="text-muted-foreground">
                            Update the information for "{part.name}"
                        </p>
                    </div>
                </div>

                {/* Form */}
                <Card>
                    <CardHeader>
                        <CardTitle>Part Details</CardTitle>
                        <CardDescription>
                            Update the information for this part
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Category Selection */}
                            <div className="space-y-2">
                                <Label htmlFor="category_id">Category *</Label>
                                <Select value={data.category_id} onValueChange={(value) => setData('category_id', value)}>
                                    <SelectTrigger className={errors.category_id ? 'border-red-500' : ''}>
                                        <SelectValue placeholder="Select a category" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {categories.map((category) => (
                                            <SelectItem key={category.id} value={category.id.toString()}>
                                                {category.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.category_id && (
                                    <p className="text-sm text-red-600">{errors.category_id}</p>
                                )}
                            </div>

                            {/* Part Name */}
                            <div className="space-y-2">
                                <Label htmlFor="name">Part Name *</Label>
                                <Input
                                    id="name"
                                    type="text"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    placeholder="e.g., LCD Display Assembly, Battery, Camera Module"
                                    className={errors.name ? 'border-red-500' : ''}
                                />
                                {errors.name && (
                                    <p className="text-sm text-red-600">{errors.name}</p>
                                )}
                            </div>

                            {/* Part Number */}
                            <div className="space-y-2">
                                <Label htmlFor="part_number">Part Number</Label>
                                <Input
                                    id="part_number"
                                    type="text"
                                    value={data.part_number}
                                    onChange={(e) => setData('part_number', e.target.value)}
                                    placeholder="e.g., LCD-001, BAT-123, CAM-456"
                                    className={errors.part_number ? 'border-red-500' : ''}
                                />
                                {errors.part_number && (
                                    <p className="text-sm text-red-600">{errors.part_number}</p>
                                )}
                            </div>

                            {/* Manufacturer */}
                            <div className="space-y-2">
                                <Label htmlFor="manufacturer">Manufacturer</Label>
                                <Input
                                    id="manufacturer"
                                    type="text"
                                    value={data.manufacturer}
                                    onChange={(e) => setData('manufacturer', e.target.value)}
                                    placeholder="e.g., Samsung, LG, Sony, Foxconn"
                                    className={errors.manufacturer ? 'border-red-500' : ''}
                                />
                                {errors.manufacturer && (
                                    <p className="text-sm text-red-600">{errors.manufacturer}</p>
                                )}
                            </div>

                            {/* Description */}
                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Detailed description of the part, its features, and compatibility notes..."
                                    className={errors.description ? 'border-red-500' : ''}
                                    rows={4}
                                />
                                {errors.description && (
                                    <p className="text-sm text-red-600">{errors.description}</p>
                                )}
                            </div>

                            {/* Specifications */}
                            <div className="space-y-4">
                                <Label>Specifications</Label>
                                <div className="space-y-3">
                                    {Object.entries(data.specifications).map(([key, value]) => (
                                        <div key={key} className="flex items-center gap-2 p-3 border rounded-lg">
                                            <div className="flex-1">
                                                <span className="font-medium text-sm">{key}:</span>
                                                <span className="ml-2 text-sm">{value}</span>
                                            </div>
                                            <Button
                                                type="button"
                                                variant="outline"
                                                size="sm"
                                                onClick={() => removeSpecification(key)}
                                                className="text-destructive hover:text-destructive"
                                            >
                                                <X className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    ))}

                                    <div className="flex gap-2">
                                        <Input
                                            placeholder="Specification name (e.g., Material, Voltage)"
                                            value={newSpecKey}
                                            onChange={(e) => setNewSpecKey(e.target.value)}
                                            className="flex-1"
                                        />
                                        <Input
                                            placeholder="Value (e.g., Glass, 3.7V)"
                                            value={newSpecValue}
                                            onChange={(e) => setNewSpecValue(e.target.value)}
                                            className="flex-1"
                                        />
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={addSpecification}
                                            disabled={!newSpecKey || !newSpecValue}
                                        >
                                            <Plus className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                                {errors.specifications && (
                                    <p className="text-sm text-red-600">{errors.specifications}</p>
                                )}
                            </div>

                            {/* Images */}
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <Label>Images</Label>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => setIsMediaPickerOpen(true)}
                                    >
                                        <ImageIcon className="h-4 w-4 mr-2" />
                                        Add Images
                                    </Button>
                                </div>

                                {data.images.length > 0 ? (
                                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                        {data.images.map((image, index) => (
                                            <div key={index} className="relative group">
                                                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 border-transparent group-hover:border-blue-200 transition-colors">
                                                    <img
                                                        src={image}
                                                        alt={`Part image ${index + 1}`}
                                                        className="w-full h-full object-cover cursor-pointer"
                                                        onClick={() => setPreviewImage(image)}
                                                        onError={(e) => {
                                                            e.currentTarget.style.display = 'none';
                                                        }}
                                                    />
                                                </div>
                                                <Button
                                                    type="button"
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => removeImage(index)}
                                                    className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                                                >
                                                    <X className="h-3 w-3" />
                                                </Button>
                                                {selectedImages[index] && (
                                                    <div className="absolute bottom-2 left-2 right-2">
                                                        <p className="text-xs text-white bg-black bg-opacity-50 rounded px-2 py-1 truncate">
                                                            {selectedImages[index].original_filename}
                                                        </p>
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                                        <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                        <h3 className="text-lg font-medium text-gray-900 mb-2">No images added</h3>
                                        <p className="text-gray-600 mb-4">Add images to showcase this part</p>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => setIsMediaPickerOpen(true)}
                                        >
                                            <ImageIcon className="h-4 w-4 mr-2" />
                                            Choose Images
                                        </Button>
                                    </div>
                                )}

                                {errors.images && (
                                    <p className="text-sm text-red-600">{errors.images}</p>
                                )}
                            </div>

                            {/* Active Status */}
                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="is_active"
                                    checked={data.is_active}
                                    onCheckedChange={(checked) => setData('is_active', checked)}
                                />
                                <Label htmlFor="is_active">Active</Label>
                            </div>

                            {/* Submit Buttons */}
                            <div className="flex justify-end gap-4 pt-6 border-t">
                                <Link href="/admin/parts">
                                    <Button variant="outline" type="button">
                                        Cancel
                                    </Button>
                                </Link>
                                <Button type="submit" disabled={processing}>
                                    <Save className="w-4 h-4 mr-2" />
                                    {processing ? 'Updating...' : 'Update Part'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
                </div>
            </div>

            {/* Media Picker */}
            <MediaPicker
                isOpen={isMediaPickerOpen}
                onClose={() => setIsMediaPickerOpen(false)}
                onSelect={handleMediaSelect}
                multiple={true}
                title="Choose Images for Part"
                acceptedTypes={['image/*']}
            />

            {/* Image Preview Modal */}
            {previewImage && (
                <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50" onClick={() => setPreviewImage(null)}>
                    <div className="max-w-4xl max-h-full p-4">
                        <img
                            src={previewImage}
                            alt="Preview"
                            className="max-w-full max-h-full object-contain"
                            onClick={(e) => e.stopPropagation()}
                        />
                        <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => setPreviewImage(null)}
                            className="absolute top-4 right-4"
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            )}
        </AppLayout>
    );
}
