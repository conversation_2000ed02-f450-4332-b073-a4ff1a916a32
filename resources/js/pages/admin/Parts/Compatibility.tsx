import { <PERSON>, <PERSON>, useForm, router } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
    ArrowLeft,
    Plus,
    Trash2,
    Smartphone,
    CheckCircle,
    Search,
    X,
    Edit
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';
import { toast } from 'sonner';

interface Category {
    id: number;
    name: string;
}

interface Brand {
    id: number;
    name: string;
    logo_url: string | null;
}

interface MobileModel {
    id: number;
    name: string;
    model_number: string | null;
    release_year: number | null;
    brand: Brand;
    pivot?: {
        compatibility_notes: string | null;
        is_verified: boolean;
    };
}

interface Part {
    id: number;
    name: string;
    part_number: string | null;
    manufacturer: string | null;
    category: Category;
    models?: MobileModel[];
}

interface Props {
    part: Part;
    availableModels: MobileModel[];
    showVerificationStatus?: boolean;
}

export default function Compatibility({ part, availableModels, showVerificationStatus = true }: Props) {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedBrand, setSelectedBrand] = useState('all');
    const [isBulkMode, setIsBulkMode] = useState(false);
    const [bulkSearchTerm, setBulkSearchTerm] = useState('');

    const [selectedBrandTags, setSelectedBrandTags] = useState<number[]>([]);
    const [selectedModelsMap, setSelectedModelsMap] = useState<Record<number, number[]>>({});
    const [activeBrandId, setActiveBrandId] = useState<number | null>(null);
    const [isBulkProcessing, setIsBulkProcessing] = useState(false);
    const { showDeleteConfirmation } = useDeleteConfirmation();

    const { data, setData, post, processing, errors, reset } = useForm({
        model_id: '',
        compatibility_notes: '',
        is_verified: false as boolean,
        display_type: '',
        display_size: '',
        location: '',
    });

    // Multi-mode form data
    const { data: multiData, setData: setMultiData, reset: resetMulti } = useForm({
        model_ids: [] as number[],
        compatibility_notes: '',
        is_verified: false as boolean,
    });

    const handleAddCompatibility = (e: React.FormEvent) => {
        e.preventDefault();
        // Flash messages from the backend will be handled by FlashMessageHandler
        post(`/admin/parts/${part.id}/compatibility`, {
            onSuccess: () => {
                reset();
            }
        });
    };

    const handleBulkAddCompatibility = (e: React.FormEvent) => {
        e.preventDefault();

        const allSelectedModels = Object.values(selectedModelsMap).flat();
        if (allSelectedModels.length === 0) {
            // Keep client-side validation toast for user experience
            toast.error('Please select at least one model.');
            return;
        }

        setIsBulkProcessing(true);

        // Use router.post directly with the exact data we want to send
        // Flash messages from the backend will be handled by FlashMessageHandler
        router.post(`/admin/parts/${part.id}/compatibility/bulk`, {
            model_ids: allSelectedModels,
            compatibility_notes: multiData.compatibility_notes,
            is_verified: multiData.is_verified
        }, {
            onSuccess: () => {
                resetMulti();
                setSelectedBrandTags([]);
                setSelectedModelsMap({});
                setActiveBrandId(null);
                setIsBulkProcessing(false);
            },
            onError: (errors) => {
                console.error('Bulk compatibility error:', errors);
                setIsBulkProcessing(false);
            }
        });
    };

    const handleRemoveCompatibility = (modelId: number, modelName: string) => {
        showDeleteConfirmation({
            title: `Remove compatibility with ${modelName}?`,
            description: "This will remove the compatibility relationship between this part and the selected model. This action cannot be undone.",
            onConfirm: () => {
                // Flash messages from the backend will be handled by FlashMessageHandler
                router.delete(`/admin/parts/${part.id}/compatibility/${modelId}`);
            }
        });
    };

    // Helper functions for new bulk interface
    const addBrandTag = (brandId: number) => {
        if (!selectedBrandTags.includes(brandId)) {
            setSelectedBrandTags(prev => {
                const newTags = [...prev, brandId];
                // Set as active if it's the first brand
                if (prev.length === 0) {
                    setActiveBrandId(brandId);
                }
                return newTags;
            });
            setSelectedModelsMap(prev => ({ ...prev, [brandId]: [] }));
        }
    };

    const removeBrandTag = (brandId: number) => {
        setSelectedBrandTags(prev => {
            const newTags = prev.filter(id => id !== brandId);
            // If removing the active brand, set the first remaining brand as active
            if (activeBrandId === brandId) {
                setActiveBrandId(newTags.length > 0 ? newTags[0] : null);
            }
            return newTags;
        });
        setSelectedModelsMap(prev => {
            const newMap = { ...prev };
            delete newMap[brandId];
            return newMap;
        });
    };

    const setActiveBrand = (brandId: number) => {
        if (selectedBrandTags.includes(brandId)) {
            setActiveBrandId(brandId);
        }
    };

    const addModelToBrand = (brandId: number, modelId: number) => {
        if (isModelAlreadyCompatible(modelId)) {
            // Keep client-side validation toast for user experience
            toast.error('This model is already compatible with this part.');
            return;
        }

        setSelectedModelsMap(prev => ({
            ...prev,
            [brandId]: [...(prev[brandId] || []), modelId]
        }));
    };

    const removeModelFromBrand = (brandId: number, modelId: number) => {
        setSelectedModelsMap(prev => ({
            ...prev,
            [brandId]: (prev[brandId] || []).filter(id => id !== modelId)
        }));
    };

    // Enhanced form submission with validation
    const handleValidatedAddCompatibility = (e: React.FormEvent) => {
        e.preventDefault();

        if (!data.model_id) {
            // Keep client-side validation toast for user experience
            toast.error('Please select a model.');
            return;
        }

        if (isModelAlreadyCompatible(parseInt(data.model_id))) {
            // Keep client-side validation toast for user experience
            toast.error('This model is already compatible with this part.');
            return;
        }

        handleAddCompatibility(e);
    };

    const handleValidatedBulkAddCompatibility = (e: React.FormEvent) => {
        e.preventDefault();

        const allSelectedModels = Object.values(selectedModelsMap).flat();
        if (allSelectedModels.length === 0) {
            // Keep client-side validation toast for user experience
            toast.error('Please select at least one model.');
            return;
        }

        const duplicateModels = allSelectedModels.filter(id => isModelAlreadyCompatible(id));
        if (duplicateModels.length > 0) {
            // Keep client-side validation toast for user experience
            toast.error('Some selected models are already compatible with this part.');
            return;
        }

        handleBulkAddCompatibility(e);
    };

    // Filter available models based on search and brand
    const filteredModels = availableModels.filter(model => {
        const matchesSearch = searchTerm === '' ||
            model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            model.brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (model.model_number && model.model_number.toLowerCase().includes(searchTerm.toLowerCase()));

        const matchesBrand = selectedBrand === 'all' || model.brand.id.toString() === selectedBrand;

        return matchesSearch && matchesBrand;
    });

    // Get unique brands from available models
    const brands = Array.from(
        new Map(availableModels.map(model => [model.brand.id, model.brand])).values()
    );

    // Validation helpers
    const isModelAlreadyCompatible = (modelId: number) => {
        return part.models?.some(model => model.id === modelId) || false;
    };

    const getAvailableModelsForBrand = (brandId: number) => {
        return availableModels.filter(model =>
            model.brand.id === brandId && !isModelAlreadyCompatible(model.id)
        );
    };

    // Get all selected models for bulk operations
    const getAllSelectedModels = () => {
        return Object.values(selectedModelsMap).flat();
    };

    return (
        <AppLayout>
            <Head title={`${part.name} - Compatibility - Parts - Admin`} />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Link href="/admin/parts">
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Parts
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Manage Compatibility</h1>
                            <p className="text-muted-foreground">
                                Configure which mobile models are compatible with "{part.name}"
                            </p>
                        </div>
                    </div>
                    <Link href={`/admin/parts/${part.id}/compatibility/edit`}>
                        <Button variant="outline">
                            <Edit className="w-4 h-4 mr-2" />
                            Table View
                        </Button>
                    </Link>
                </div>



                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Current Compatible Models */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <CheckCircle className="w-5 h-5 text-green-600" />
                                Compatible Models ({part.models?.length || 0})
                            </CardTitle>
                            <CardDescription>
                                Models currently compatible with this part
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {part.models && part.models.length > 0 ? (
                                <div className="space-y-3">
                                    {part.models.map((model) => (
                                        <div key={model.id} className="flex items-center justify-between p-3 border rounded-lg">
                                            <div className="flex items-center gap-3">
                                                {model.brand.logo_url && (
                                                    <img 
                                                        src={model.brand.logo_url} 
                                                        alt={model.brand.name}
                                                        className="w-8 h-8 object-contain"
                                                    />
                                                )}
                                                <div>
                                                    <p className="font-medium">{model.brand.name} {model.name}</p>
                                                    {model.model_number && (
                                                        <p className="text-sm text-muted-foreground">{model.model_number}</p>
                                                    )}
                                                    {model.pivot?.compatibility_notes && (
                                                        <p className="text-sm text-muted-foreground mt-1">
                                                            {model.pivot.compatibility_notes}
                                                        </p>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <Badge variant={model.pivot?.is_verified ? "default" : "secondary"}>
                                                    {model.pivot?.is_verified ? 'Verified' : 'Unverified'}
                                                </Badge>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handleRemoveCompatibility(model.id, `${model.brand.name} ${model.name}`)}
                                                    className="text-destructive hover:text-destructive"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8">
                                    <Smartphone className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                    <h3 className="text-lg font-medium mb-2">No compatible models</h3>
                                    <p className="text-muted-foreground">
                                        Add compatible models using the form on the right.
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Add New Compatibility */}
                    <Card>
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <Plus className="w-5 h-5 text-blue-600" />
                                    <CardTitle>
                                        {isBulkMode ? 'Add Bulk Compatibility' : 'Add Compatibility'}
                                    </CardTitle>
                                </div>
                                <div className="flex items-center gap-2">
                                    <span className="text-sm font-medium">Multi-Brand Bulk</span>
                                    <Switch
                                        checked={isBulkMode}
                                        onCheckedChange={setIsBulkMode}
                                    />
                                </div>
                            </div>
                            <CardDescription>
                                {isBulkMode
                                    ? 'Add multiple model compatibilities across brands for this part'
                                    : 'Add a new model compatibility for this part'
                                }
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {!isBulkMode ? (
                                <>
                                    {/* Single Mode - Search and Filter */}
                                    <div className="space-y-4 mb-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="search">Search Models</Label>
                                            <div className="relative">
                                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                                                <Input
                                                    id="search"
                                                    placeholder="Search by model name, brand, or model number..."
                                                    value={searchTerm}
                                                    onChange={(e) => setSearchTerm(e.target.value)}
                                                    className="pl-10"
                                                />
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="brand-filter">Filter by Brand</Label>
                                            <Select value={selectedBrand} onValueChange={setSelectedBrand}>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="All brands" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">All brands</SelectItem>
                                                    {brands.map((brand) => (
                                                        <SelectItem key={brand.id} value={brand.id.toString()}>
                                                            <div className="flex items-center gap-2">
                                                                {brand.logo_url && (
                                                                    <img
                                                                        src={brand.logo_url}
                                                                        alt={brand.name}
                                                                        className="w-4 h-4 object-contain"
                                                                    />
                                                                )}
                                                                {brand.name}
                                                            </div>
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </div>
                                </>
                            ) : (
                                <>
                                    {/* Bulk Mode - New Design */}
                                    <div className="space-y-6">
                                        {/* Search Models */}
                                        <div className="space-y-2">
                                            <Label htmlFor="bulk-search">Search Models</Label>
                                            <div className="relative">
                                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                                                <Input
                                                    id="bulk-search"
                                                    placeholder="Search by model name, brand, or model number..."
                                                    value={bulkSearchTerm}
                                                    onChange={(e) => setBulkSearchTerm(e.target.value)}
                                                    className="pl-10"
                                                />
                                            </div>
                                        </div>

                                        {/* Filter by Brand */}
                                        <div className="space-y-2">
                                            <Label htmlFor="bulk-brand-filter">Filter by Brand</Label>
                                            <Select
                                                value=""
                                                onValueChange={(value) => {
                                                    if (value !== "all" && value !== "") {
                                                        const brandId = parseInt(value);
                                                        addBrandTag(brandId);
                                                    }
                                                }}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="All brands" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">All brands</SelectItem>
                                                    {brands
                                                        .filter(brand => !selectedBrandTags.includes(brand.id))
                                                        .filter(brand => getAvailableModelsForBrand(brand.id).length > 0)
                                                        .map((brand) => (
                                                        <SelectItem key={brand.id} value={brand.id.toString()}>
                                                            <div className="flex items-center gap-2">
                                                                {brand.logo_url && (
                                                                    <img
                                                                        src={brand.logo_url}
                                                                        alt={brand.name}
                                                                        className="w-4 h-4 object-contain"
                                                                    />
                                                                )}
                                                                {brand.name}
                                                                <span className="text-xs text-muted-foreground">
                                                                    ({getAvailableModelsForBrand(brand.id).length} models)
                                                                </span>
                                                            </div>
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </div>
                                </>
                            )}

                            {!isBulkMode ? (
                                <form onSubmit={handleValidatedAddCompatibility} className="space-y-4">
                                    {/* Model Selection */}
                                    <div className="space-y-2">
                                        <Label htmlFor="model_id">Select Model *</Label>
                                        <Select value={data.model_id} onValueChange={(value) => setData('model_id', value)}>
                                            <SelectTrigger className={errors.model_id ? 'border-red-500' : ''}>
                                                <SelectValue placeholder="Choose a model" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {filteredModels
                                                    .filter(model => !isModelAlreadyCompatible(model.id))
                                                    .map((model) => (
                                                    <SelectItem key={model.id} value={model.id.toString()}>
                                                        <div className="flex items-center gap-2">
                                                            {model.brand.logo_url && (
                                                                <img
                                                                    src={model.brand.logo_url}
                                                                    alt={model.brand.name}
                                                                    className="w-4 h-4 object-contain"
                                                                />
                                                            )}
                                                            <span>{model.brand.name} {model.name}</span>
                                                            {model.model_number && (
                                                                <span className="text-muted-foreground">({model.model_number})</span>
                                                            )}
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                                {filteredModels.filter(model => !isModelAlreadyCompatible(model.id)).length === 0 && (
                                                    <div className="p-2 text-center text-muted-foreground text-sm">
                                                        No available models found
                                                    </div>
                                                )}
                                            </SelectContent>
                                        </Select>
                                        {errors.model_id && (
                                            <p className="text-sm text-red-600">{errors.model_id}</p>
                                        )}
                                    </div>

                                    {/* Display Type */}
                                    <div className="space-y-2">
                                        <Label htmlFor="display_type">Display Type</Label>
                                        <Input
                                            id="display_type"
                                            value={data.display_type}
                                            onChange={(e) => setData('display_type', e.target.value)}
                                            placeholder="e.g., OLED, LCD, AMOLED"
                                        />
                                    </div>

                                    {/* Display Size */}
                                    <div className="space-y-2">
                                        <Label htmlFor="display_size">Display Size</Label>
                                        <Input
                                            id="display_size"
                                            value={data.display_size}
                                            onChange={(e) => setData('display_size', e.target.value)}
                                            placeholder="e.g., 6.1 inches, 5.4 inches"
                                        />
                                    </div>

                                    {/* Location */}
                                    <div className="space-y-2">
                                        <Label htmlFor="location">Location</Label>
                                        <Input
                                            id="location"
                                            value={data.location}
                                            onChange={(e) => setData('location', e.target.value)}
                                            placeholder="e.g., Front, Back, Internal"
                                        />
                                    </div>

                                    {/* Compatibility Notes */}
                                    <div className="space-y-2">
                                        <Label htmlFor="compatibility_notes">Compatibility Notes</Label>
                                        <Textarea
                                            id="compatibility_notes"
                                            value={data.compatibility_notes}
                                            onChange={(e) => setData('compatibility_notes', e.target.value)}
                                            placeholder="Optional notes about compatibility, installation requirements, etc."
                                            rows={3}
                                        />
                                    </div>

                                    {/* Verified Status */}
                                    {showVerificationStatus && (
                                        <div className="flex items-center space-x-2">
                                            <Switch
                                                id="is_verified"
                                                checked={data.is_verified}
                                                onCheckedChange={(checked) => setData('is_verified', checked)}
                                            />
                                            <Label htmlFor="is_verified">Mark as verified</Label>
                                        </div>
                                    )}

                                    {/* Submit Button */}
                                    <Button type="submit" disabled={processing} className="w-full">
                                        <Plus className="w-4 h-4 mr-2" />
                                        {processing ? 'Adding...' : 'Add Compatibility'}
                                    </Button>
                                </form>
                            ) : (
                                <form onSubmit={handleValidatedBulkAddCompatibility} className="space-y-6">
                                    {/* Selected Brands Tags */}
                                    <div className="space-y-2">
                                        <Label>Selected Brands *</Label>
                                        <div className="flex flex-wrap gap-2 min-h-[40px] p-3 border rounded-lg bg-muted/20">
                                            {selectedBrandTags.length > 0 ? (
                                                selectedBrandTags.map((brandId) => {
                                                    const brand = brands.find(b => b.id === brandId);
                                                    const isActive = activeBrandId === brandId;
                                                    return (
                                                        <div key={brandId} className="flex items-center gap-1">
                                                            <Button
                                                                type="button"
                                                                variant={isActive ? "default" : "secondary"}
                                                                size="sm"
                                                                className={`flex items-center gap-1 ${isActive ? 'bg-orange-500 hover:bg-orange-600 text-white' : ''}`}
                                                                onClick={() => setActiveBrand(brandId)}
                                                            >
                                                                {brand?.logo_url && (
                                                                    <img
                                                                        src={brand.logo_url}
                                                                        alt={brand.name}
                                                                        className="w-4 h-4 object-contain"
                                                                    />
                                                                )}
                                                                {brand?.name}
                                                            </Button>
                                                            <Button
                                                                type="button"
                                                                variant="ghost"
                                                                size="sm"
                                                                className="h-6 w-6 p-0 hover:bg-destructive hover:text-destructive-foreground"
                                                                onClick={() => removeBrandTag(brandId)}
                                                            >
                                                                <X className="h-3 w-3" />
                                                            </Button>
                                                        </div>
                                                    );
                                                })
                                            ) : (
                                                <span className="text-muted-foreground text-sm">
                                                    Use the filter above to select brands
                                                </span>
                                            )}
                                        </div>
                                    </div>

                                    {/* Single Model Selection Field */}
                                    <div className="space-y-2">
                                        <Label>Select Models *</Label>
                                        {activeBrandId ? (
                                            <Select
                                                value=""
                                                onValueChange={(value) => {
                                                    const modelId = parseInt(value);
                                                    const selectedModels = selectedModelsMap[activeBrandId] || [];
                                                    if (!selectedModels.includes(modelId)) {
                                                        addModelToBrand(activeBrandId, modelId);
                                                    }
                                                }}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder={`${brands.find(b => b.id === activeBrandId)?.name}: Choose a model`} />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {getAvailableModelsForBrand(activeBrandId).map((model) => {
                                                        const selectedModels = selectedModelsMap[activeBrandId] || [];
                                                        return (
                                                            <SelectItem
                                                                key={model.id}
                                                                value={model.id.toString()}
                                                                disabled={selectedModels.includes(model.id)}
                                                            >
                                                                <div className="flex items-center gap-2">
                                                                    <span>{model.name}</span>
                                                                    {model.model_number && (
                                                                        <span className="text-muted-foreground">({model.model_number})</span>
                                                                    )}
                                                                </div>
                                                            </SelectItem>
                                                        );
                                                    })}
                                                </SelectContent>
                                            </Select>
                                        ) : (
                                            <div className="p-3 border rounded-lg bg-muted/20 text-center text-muted-foreground">
                                                {selectedBrandTags.length > 0
                                                    ? "Click on a brand above to select models"
                                                    : "Add brands first to select models"
                                                }
                                            </div>
                                        )}
                                    </div>

                                    {/* Single Compatible Models Display */}
                                    {activeBrandId && (selectedModelsMap[activeBrandId] || []).length > 0 && (
                                        <div className="space-y-2">
                                            <Label className="text-sm font-medium">
                                                {brands.find(b => b.id === activeBrandId)?.name} Compatible Models
                                            </Label>
                                            <div className="flex flex-wrap gap-2 p-3 border rounded-lg bg-muted/10">
                                                {(selectedModelsMap[activeBrandId] || []).map((modelId) => {
                                                    const model = getAvailableModelsForBrand(activeBrandId).find(m => m.id === modelId);
                                                    return (
                                                        <Badge key={modelId} variant="outline" className="flex items-center gap-1">
                                                            {model?.name}
                                                            {model?.model_number && (
                                                                <span className="text-muted-foreground">({model.model_number})</span>
                                                            )}
                                                            <Button
                                                                type="button"
                                                                variant="ghost"
                                                                size="sm"
                                                                className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                                                                onClick={() => removeModelFromBrand(activeBrandId, modelId)}
                                                            >
                                                                <X className="h-3 w-3" />
                                                            </Button>
                                                        </Badge>
                                                    );
                                                })}
                                            </div>
                                        </div>
                                    )}

                                    {/* Compatibility Notes */}
                                    <div className="space-y-2">
                                        <Label htmlFor="bulk_compatibility_notes">Compatibility Notes (Applied to All)</Label>
                                        <Textarea
                                            id="bulk_compatibility_notes"
                                            value={multiData.compatibility_notes}
                                            onChange={(e) => setMultiData('compatibility_notes', e.target.value)}
                                            placeholder="Optional notes about compatibility, installation requirements, etc. (will be applied to all selected models)"
                                            rows={3}
                                        />
                                    </div>

                                    {/* Mark as verified */}
                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            id="bulk_is_verified"
                                            checked={multiData.is_verified}
                                            onCheckedChange={(checked) => setMultiData('is_verified', checked)}
                                        />
                                        <Label htmlFor="bulk_is_verified">Mark as verified</Label>
                                    </div>

                                    {/* Selection Summary */}
                                    {getAllSelectedModels().length > 0 && (
                                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                            <h4 className="font-medium text-blue-900 mb-2">Selection Summary</h4>
                                            <div className="text-sm text-blue-800">
                                                <p>• {selectedBrandTags.length} brands selected</p>
                                                <p>• {getAllSelectedModels().length} models selected</p>
                                                <p>• Compatibility will be added for all selected models</p>
                                            </div>
                                        </div>
                                    )}

                                    {/* Submit Button */}
                                    <Button
                                        type="submit"
                                        disabled={isBulkProcessing || getAllSelectedModels().length === 0}
                                        className="w-full bg-orange-500 hover:bg-orange-600"
                                        size="lg"
                                    >
                                        <Plus className="w-4 h-4 mr-2" />
                                        {isBulkProcessing
                                            ? 'Adding Compatibilities...'
                                            : getAllSelectedModels().length === 0
                                                ? 'Select Models to Continue'
                                                : `Add ${getAllSelectedModels().length} Compatibility${getAllSelectedModels().length === 1 ? '' : 'ies'}`
                                        }
                                    </Button>
                                </form>
                            )}
                        </CardContent>
                    </Card>
                </div>
                </div>
            </div>
        </AppLayout>
    );
}
