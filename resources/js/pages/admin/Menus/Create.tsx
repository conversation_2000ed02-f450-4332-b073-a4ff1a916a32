import { Head, Link, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ArrowLeft, Save, Menu as MenuIcon } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface Props {
    locations: Record<string, string>;
}

export default function Create({ locations }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        location: 'header',
        description: '',
        is_active: true as boolean,
    });

    // Handle form submission
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Flash messages from the backend will be handled by FlashMessageHandler
        post('/admin/menus');
    };

    return (
        <AppLayout>
            <Head title="Create Menu" />

            <div className="p-6 space-y-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="ghost" size="icon" asChild>
                            <Link href="/admin/menus">
                                <ArrowLeft className="h-4 w-4" />
                                <span className="sr-only">Back to menus</span>
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Create Menu</h1>
                            <p className="text-muted-foreground">Create a new navigation menu for your website</p>
                        </div>
                    </div>
                    <Button
                        type="submit"
                        form="menu-form"
                        disabled={processing}
                    >
                        <Save className="mr-2 h-4 w-4" />
                        Create Menu
                    </Button>
                </div>

                <form id="menu-form" onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Main Content */}
                        <div className="lg:col-span-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <MenuIcon className="h-5 w-5" />
                                        Menu Details
                                    </CardTitle>
                                    <CardDescription>
                                        Configure the basic settings for your menu
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="name">Menu Name *</Label>
                                        <Input
                                            id="name"
                                            type="text"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="Enter menu name"
                                            className={errors.name ? 'border-red-500' : ''}
                                        />
                                        {errors.name && (
                                            <p className="text-sm text-red-500">{errors.name}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="description">Description</Label>
                                        <Textarea
                                            id="description"
                                            value={data.description}
                                            onChange={(e) => setData('description', e.target.value)}
                                            placeholder="Optional description for this menu"
                                            className={`min-h-[80px] ${errors.description ? 'border-red-500' : ''}`}
                                        />
                                        {errors.description && (
                                            <p className="text-sm text-red-500">{errors.description}</p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Menu Settings */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Menu Settings</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="location">Location *</Label>
                                        <Select
                                            value={data.location}
                                            onValueChange={(value) => setData('location', value)}
                                        >
                                            <SelectTrigger className={errors.location ? 'border-red-500' : ''}>
                                                <SelectValue placeholder="Select location" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(locations).map(([key, name]) => (
                                                    <SelectItem key={key} value={key}>
                                                        {name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.location && (
                                            <p className="text-sm text-red-500">{errors.location}</p>
                                        )}
                                        <p className="text-xs text-muted-foreground">
                                            Choose where this menu will be displayed on your website
                                        </p>
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <Label htmlFor="is_active">Active</Label>
                                        <Switch
                                            id="is_active"
                                            checked={data.is_active}
                                            onCheckedChange={(checked: boolean) => setData('is_active', checked)}
                                        />
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                        Only active menus will be displayed on your website
                                    </p>
                                </CardContent>
                            </Card>

                            {/* Help */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Next Steps</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-sm text-muted-foreground space-y-2">
                                        <p>After creating the menu, you'll be able to:</p>
                                        <ul className="list-disc list-inside space-y-1">
                                            <li>Add menu items</li>
                                            <li>Organize items with drag & drop</li>
                                            <li>Create nested sub-menus</li>
                                            <li>Link to pages, categories, and external URLs</li>
                                        </ul>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
