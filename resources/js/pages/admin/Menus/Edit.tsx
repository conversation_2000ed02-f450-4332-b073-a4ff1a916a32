import { Head, Link, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ArrowLeft, Save, Menu as MenuIcon, Trash2 } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';
import { router } from '@inertiajs/react';

interface Menu {
    id: number;
    name: string;
    location: string;
    description: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
}

interface Props {
    menu: Menu;
    locations: Record<string, string>;
}

export default function Edit({ menu, locations }: Props) {
    const { confirmDelete } = useDeleteConfirmation();

    const { data, setData, put, processing, errors } = useForm({
        name: menu.name,
        location: menu.location,
        description: menu.description || '',
        is_active: menu.is_active,
    });

    // Handle form submission
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Flash messages from the backend will be handled by FlashMessageHandler
        put(`/admin/menus/${menu.id}`);
    };

    // Handle menu deletion
    const handleDelete = () => {
        confirmDelete({
            title: `Delete Menu: ${menu.name}`,
            description: 'Are you sure you want to delete this menu? This action cannot be undone and will remove all menu items.',
            confirmText: 'Delete Menu',
            cancelText: 'Cancel',
            onConfirm: () => {
                // Flash messages from the backend will be handled by FlashMessageHandler
                router.delete(`/admin/menus/${menu.id}`);
            },
        });
    };

    return (
        <AppLayout>
            <Head title={`Edit Menu: ${menu.name}`} />

            <div className="p-6 space-y-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="ghost" size="icon" asChild>
                            <Link href="/admin/menus">
                                <ArrowLeft className="h-4 w-4" />
                                <span className="sr-only">Back to menus</span>
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Edit Menu</h1>
                            <p className="text-muted-foreground">Update menu settings and configuration</p>
                        </div>
                    </div>
                    <div className="flex gap-2">
                        <Button
                            type="button"
                            variant="destructive"
                            onClick={handleDelete}
                            disabled={processing}
                        >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                        </Button>
                        <Button
                            type="submit"
                            form="menu-form"
                            disabled={processing}
                        >
                            <Save className="mr-2 h-4 w-4" />
                            Update Menu
                        </Button>
                    </div>
                </div>

                <form id="menu-form" data-testid="menu-form" onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Main Content */}
                        <div className="lg:col-span-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <MenuIcon className="h-5 w-5" />
                                        Menu Details
                                    </CardTitle>
                                    <CardDescription>
                                        Update the basic settings for your menu
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="name">Menu Name *</Label>
                                        <Input
                                            id="name"
                                            type="text"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="Enter menu name"
                                            className={errors.name ? 'border-red-500' : ''}
                                        />
                                        {errors.name && (
                                            <p className="text-sm text-red-500">{errors.name}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="description">Description</Label>
                                        <Textarea
                                            id="description"
                                            value={data.description}
                                            onChange={(e) => setData('description', e.target.value)}
                                            placeholder="Optional description for this menu"
                                            className={`min-h-[80px] ${errors.description ? 'border-red-500' : ''}`}
                                        />
                                        {errors.description && (
                                            <p className="text-sm text-red-500">{errors.description}</p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Menu Settings */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Menu Settings</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="location">Location *</Label>
                                        <Select
                                            value={data.location}
                                            onValueChange={(value) => setData('location', value)}
                                        >
                                            <SelectTrigger className={errors.location ? 'border-red-500' : ''}>
                                                <SelectValue placeholder="Select location" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(locations).map(([key, name]) => (
                                                    <SelectItem key={key} value={key}>
                                                        {name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.location && (
                                            <p className="text-sm text-red-500">{errors.location}</p>
                                        )}
                                        <p className="text-xs text-muted-foreground">
                                            Choose where this menu will be displayed on your website
                                        </p>
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <Label htmlFor="is_active">Active</Label>
                                        <Switch
                                            id="is_active"
                                            checked={data.is_active}
                                            onCheckedChange={(checked) => setData('is_active', checked)}
                                        />
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                        Only active menus will be displayed on your website
                                    </p>
                                </CardContent>
                            </Card>

                            {/* Menu Info */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Menu Information</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Created:</span>
                                        <span>{new Date(menu.created_at).toLocaleDateString()}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Updated:</span>
                                        <span>{new Date(menu.updated_at).toLocaleDateString()}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Location:</span>
                                        <span>{locations[menu.location] || menu.location}</span>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Quick Actions */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Quick Actions</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <Button asChild className="w-full">
                                        <Link href={`/admin/menus/${menu.id}`}>
                                            Manage Menu Items
                                        </Link>
                                    </Button>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
