// Components
import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import AuthLayout from '@/layouts/auth-layout';

export default function VerifyEmail({ status }: { status?: string }) {
    const { post, processing } = useForm({});

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('verification.send'));
    };

    return (
        <AuthLayout
            title="Verify Your Email Address"
            description="We've sent a verification link to your email address. Please check your inbox and click the link to verify your account."
        >
            <Head title="Email Verification" />

            {status === 'registration-successful' && (
                <div className="mb-6 text-center p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="text-sm font-medium text-blue-800 mb-2">
                        🎉 Registration Successful!
                    </div>
                    <div className="text-sm text-blue-700">
                        We've sent a verification email to your inbox. Please check your email and click the verification link to complete your account setup.
                    </div>
                </div>
            )}

            {status === 'verification-link-sent' && (
                <div className="mb-4 text-center text-sm font-medium text-green-600 p-3 bg-green-50 border border-green-200 rounded-lg">
                    ✅ A new verification link has been sent to your email address.
                </div>
            )}

            <div className="space-y-6 text-center">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                    Didn't receive the email? Check your spam folder or click below to resend.
                </div>

                <form onSubmit={submit}>
                    <Button disabled={processing} variant="secondary" className="w-full">
                        {processing && <LoaderCircle className="h-4 w-4 animate-spin mr-2" />}
                        Resend Verification Email
                    </Button>
                </form>

                <TextLink href={route('logout')} method="post" className="mx-auto block text-sm text-gray-500 hover:text-gray-700">
                    Sign out
                </TextLink>
            </div>
        </AuthLayout>
    );
}
