<?php

namespace Tests\Feature\Admin;

use App\Models\PricingPlan;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PricingPlanUpdateTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user using one of the valid admin emails
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'subscription_plan' => 'premium',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);
    }

    /** @test */
    public function free_plan_can_be_updated_without_payment_integration_requirements()
    {
        // Create a free plan with payment methods disabled
        $freePlan = PricingPlan::factory()->create([
            'name' => 'free',
            'price' => 0,
            'online_payment_enabled' => false,
            'offline_payment_enabled' => false,
            'crypto_payment_enabled' => false,
            'paddle_price_id_monthly' => null,
            'coinbase_commerce_price_id_monthly' => null,
        ]);

        $updateData = [
            'name' => 'free',
            'display_name' => 'Updated Free Plan',
            'description' => 'Updated description for free plan',
            'price' => 0,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['Updated feature 1', 'Updated feature 2'],
            'search_limit' => 25,
            'is_active' => true,
            'is_default' => true,
            'is_popular' => false,
            'sort_order' => 1,
            'metadata' => ['color' => 'blue'],
            
            // Payment method settings (should remain disabled for free plans)
            'online_payment_enabled' => false,
            'offline_payment_enabled' => false,
            'crypto_payment_enabled' => false,
            
            // Payment integration fields (should be null for free plans)
            'paddle_price_id_monthly' => '',
            'paddle_price_id_yearly' => '',
            'paddle_product_id' => '',
            'shurjopay_price_id_monthly' => '',
            'shurjopay_price_id_yearly' => '',
            'shurjopay_product_id' => '',
            'coinbase_commerce_price_id_monthly' => '',
            'coinbase_commerce_price_id_yearly' => '',
            'coinbase_commerce_product_id' => '',
            
            // Fee configurations (should be zero for free plans)
            'paddle_fee_percentage' => 0,
            'paddle_fee_fixed' => 0,
            'shurjopay_fee_percentage' => 0,
            'shurjopay_fee_fixed' => 0,
            'coinbase_commerce_fee_percentage' => 0,
            'coinbase_commerce_fee_fixed' => 0,
            'offline_fee_percentage' => 0,
            'offline_fee_fixed' => 0,
            'tax_percentage' => 0,
            'show_fees_breakdown' => false,
            'tax_inclusive' => false,
        ];

        $response = $this->actingAs($this->admin)
            ->put("/admin/pricing-plans/{$freePlan->id}", $updateData);

        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Verify the plan was updated
        $freePlan->refresh();
        $this->assertEquals('Updated Free Plan', $freePlan->display_name);
        $this->assertEquals('Updated description for free plan', $freePlan->description);
        $this->assertEquals(25, $freePlan->search_limit);
        
        // Verify payment methods remain disabled
        $this->assertFalse($freePlan->online_payment_enabled);
        $this->assertFalse($freePlan->offline_payment_enabled);
        $this->assertFalse($freePlan->crypto_payment_enabled);
        
        // Verify payment integration fields are null
        $this->assertNull($freePlan->paddle_price_id_monthly);
        $this->assertNull($freePlan->coinbase_commerce_price_id_monthly);
    }

    /** @test */
    public function paid_plan_requires_payment_integration_when_payment_methods_enabled()
    {
        // Create a paid plan with payment methods enabled
        $paidPlan = PricingPlan::factory()->create([
            'name' => 'premium',
            'price' => 29.99,
            'online_payment_enabled' => true,
            'crypto_payment_enabled' => true,
        ]);

        $updateData = [
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'description' => 'Premium plan description',
            'price' => 39.99,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['Premium feature 1', 'Premium feature 2'],
            'search_limit' => 1000,
            'is_active' => true,
            'is_default' => false,
            'is_popular' => true,
            'sort_order' => 2,
            'metadata' => ['color' => 'gold'],
            
            // Payment methods enabled
            'online_payment_enabled' => true,
            'offline_payment_enabled' => false,
            'crypto_payment_enabled' => true,
            
            // Missing required payment integration fields
            'paddle_price_id_monthly' => '', // This should cause validation error
            'coinbase_commerce_price_id_monthly' => '', // This should cause validation error
        ];

        $response = $this->actingAs($this->admin)
            ->put("/admin/pricing-plans/{$paidPlan->id}", $updateData);

        $response->assertSessionHasErrors([
            'paddle_price_id_monthly',
            'coinbase_commerce_price_id_monthly'
        ]);
    }

    /** @test */
    public function paid_plan_can_be_updated_with_valid_payment_integration_data()
    {
        // Create a paid plan
        $paidPlan = PricingPlan::factory()->create([
            'name' => 'premium',
            'price' => 29.99,
        ]);

        $updateData = [
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'description' => 'Premium plan description',
            'price' => 39.99,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['Premium feature 1', 'Premium feature 2'],
            'search_limit' => 1000,
            'is_active' => true,
            'is_default' => false,
            'is_popular' => true,
            'sort_order' => 2,
            'metadata' => ['color' => 'gold'],
            
            // Payment methods enabled with valid integration data
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => true,
            
            // Valid payment integration fields
            'paddle_price_id_monthly' => 'pri_01234567890',
            'paddle_price_id_yearly' => 'pri_01234567891',
            'paddle_product_id' => 'pro_01234567890',
            'coinbase_commerce_price_id_monthly' => 'cc_01234567890',
            'coinbase_commerce_price_id_yearly' => 'cc_01234567891',
            'coinbase_commerce_product_id' => 'cc_pro_01234567890',
            
            // Fee configurations
            'paddle_fee_percentage' => 5.0,
            'paddle_fee_fixed' => 0.50,
            'coinbase_commerce_fee_percentage' => 3.0,
            'coinbase_commerce_fee_fixed' => 0.30,
            'offline_fee_percentage' => 0,
            'offline_fee_fixed' => 0,
            'tax_percentage' => 10.0,
            'show_fees_breakdown' => true,
            'tax_inclusive' => false,
        ];

        $response = $this->actingAs($this->admin)
            ->put("/admin/pricing-plans/{$paidPlan->id}", $updateData);

        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Verify the plan was updated
        $paidPlan->refresh();
        $this->assertEquals('Premium Plan', $paidPlan->display_name);
        $this->assertEquals(39.99, $paidPlan->price);
        $this->assertEquals(1000, $paidPlan->search_limit);
        
        // Verify payment methods are enabled
        $this->assertTrue($paidPlan->online_payment_enabled);
        $this->assertTrue($paidPlan->offline_payment_enabled);
        $this->assertTrue($paidPlan->crypto_payment_enabled);
        
        // Verify payment integration fields are set
        $this->assertEquals('pri_01234567890', $paidPlan->paddle_price_id_monthly);
        $this->assertEquals('cc_01234567890', $paidPlan->coinbase_commerce_price_id_monthly);
    }
}
