<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\URL;
use Tests\TestCase;

class EmailVerificationFlowTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Fake mail and notifications for testing
        Mail::fake();
        Notification::fake();
    }

    public function test_complete_registration_and_verification_flow()
    {
        Event::fake();

        // Step 1: Register a new user
        $response = $this->postWithCsrf('/register', [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        // Should redirect to verification notice
        $response->assertRedirect(route('verification.notice'));
        $response->assertSessionHas('status', 'registration-successful');

        // User should be authenticated but not verified
        $this->assertAuthenticated();
        $user = auth()->user();
        $this->assertFalse($user->hasVerifiedEmail());

        // Registered event should be fired
        Event::assertDispatched(Registered::class);

        // Step 2: Try to access dashboard - should be redirected to verification
        $dashboardResponse = $this->get('/dashboard');
        $dashboardResponse->assertRedirect(route('verification.notice'));

        // Step 3: Access verification notice page
        $verificationResponse = $this->get('/verify-email');
        $verificationResponse->assertStatus(200);
        $verificationResponse->assertInertia(fn ($page) => 
            $page->component('auth/verify-email')
        );

        // Step 4: Resend verification email
        $resendResponse = $this->post('/email/verification-notification');
        $resendResponse->assertRedirect();
        $resendResponse->assertSessionHas('status', 'verification-link-sent');

        // Step 5: Verify email using signed URL
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $user->id, 'hash' => sha1($user->email)]
        );

        $verifyResponse = $this->get($verificationUrl);

        // Should redirect to dashboard after verification (without query parameter check for now)
        $verifyResponse->assertRedirect();

        // User should now be verified
        $user->refresh();
        $this->assertTrue($user->hasVerifiedEmail());

        // Verified event should be fired
        Event::assertDispatched(Verified::class);

        // Step 6: Now user should be able to access dashboard
        $finalDashboardResponse = $this->get('/dashboard');
        $finalDashboardResponse->assertStatus(200);
    }

    public function test_admin_user_verification_flow()
    {
        Event::fake();

        // Register an admin user
        $response = $this->postWithCsrf('/register', [
            'name' => 'Admin User',
            'email' => '<EMAIL>', // Admin email
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        // Should redirect to verification notice
        $response->assertRedirect(route('verification.notice'));

        $user = auth()->user();
        $this->assertTrue($user->isAdmin());
        $this->assertFalse($user->hasVerifiedEmail());

        // Verify email
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $user->id, 'hash' => sha1($user->email)]
        );

        $verifyResponse = $this->get($verificationUrl);
        
        // Admin should be redirected to admin dashboard after verification
        $verifyResponse->assertRedirect(route('admin.dashboard', absolute: false) . '?verified=1');

        // User should now be verified
        $user->refresh();
        $this->assertTrue($user->hasVerifiedEmail());
    }

    public function test_already_verified_user_redirected_from_verification_notice()
    {
        // Create and verify a user
        $user = User::factory()->create([
            'email_verified_at' => now(),
        ]);

        $this->actingAs($user);

        // Try to access verification notice
        $response = $this->get('/verify-email');
        
        // Should be redirected to dashboard since already verified
        $response->assertRedirect(route('dashboard', absolute: false));
    }

    public function test_already_verified_admin_redirected_to_admin_dashboard()
    {
        // Create and verify an admin user
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
        ]);

        $this->actingAs($admin);

        // Try to access verification notice
        $response = $this->get('/verify-email');
        
        // Should be redirected to admin dashboard since already verified
        $response->assertRedirect(route('admin.dashboard', absolute: false));
    }

    public function test_verification_link_with_invalid_signature_fails()
    {
        $user = User::factory()->unverified()->create();
        $this->actingAs($user);

        // Create an invalid verification URL (wrong hash)
        $invalidUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $user->id, 'hash' => 'invalid-hash']
        );

        $response = $this->get($invalidUrl);
        
        // Should return 403 Forbidden
        $response->assertStatus(403);

        // User should still not be verified
        $user->refresh();
        $this->assertFalse($user->hasVerifiedEmail());
    }

    public function test_verification_link_with_expired_signature_fails()
    {
        $user = User::factory()->unverified()->create();
        $this->actingAs($user);

        // Create an expired verification URL
        $expiredUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->subMinutes(60), // Expired 1 hour ago
            ['id' => $user->id, 'hash' => sha1($user->email)]
        );

        $response = $this->get($expiredUrl);
        
        // Should return 403 Forbidden
        $response->assertStatus(403);

        // User should still not be verified
        $user->refresh();
        $this->assertFalse($user->hasVerifiedEmail());
    }

    public function test_resend_verification_email_for_already_verified_user()
    {
        // Create and verify a user
        $user = User::factory()->create([
            'email_verified_at' => now(),
        ]);

        $this->actingAs($user);

        // Try to resend verification email
        $response = $this->post('/email/verification-notification');
        
        // Should be redirected to dashboard since already verified
        $response->assertRedirect(route('dashboard', absolute: false));
    }

    public function test_guest_cannot_access_verification_routes()
    {
        // Try to access verification notice as guest
        $response = $this->get('/verify-email');
        $response->assertRedirect(route('login'));

        // Try to resend verification email as guest
        $resendResponse = $this->post('/email/verification-notification');
        $resendResponse->assertRedirect(route('login'));
    }

    public function test_verification_notice_shows_correct_status_messages()
    {
        $user = User::factory()->unverified()->create();
        $this->actingAs($user);

        // Test registration successful status (passed via session)
        $response = $this->withSession(['status' => 'registration-successful'])
            ->get('/verify-email');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('auth/verify-email')
                ->has('status')
                ->where('status', 'registration-successful')
        );

        // Test verification link sent status (passed via session)
        $response = $this->withSession(['status' => 'verification-link-sent'])
            ->get('/verify-email');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('auth/verify-email')
                ->has('status')
                ->where('status', 'verification-link-sent')
        );
    }
}
