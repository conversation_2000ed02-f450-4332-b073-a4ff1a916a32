<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\UserNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserNotificationModelTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected User $admin;
    protected UserNotification $notification;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->admin = User::factory()->admin()->create();

        $this->notification = UserNotification::create([
            'user_id' => $this->user->id,
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'type' => 'info',
            'sent_by' => $this->admin->id,
        ]);
    }

    public function test_notification_belongs_to_user()
    {
        $this->assertInstanceOf(User::class, $this->notification->user);
        $this->assertEquals($this->user->id, $this->notification->user->id);
    }

    public function test_notification_belongs_to_sender()
    {
        $this->assertInstanceOf(User::class, $this->notification->sentBy);
        $this->assertEquals($this->admin->id, $this->notification->sentBy->id);
    }

    public function test_notification_is_unread_by_default()
    {
        $this->assertFalse($this->notification->isRead());
        $this->assertNull($this->notification->read_at);
    }

    public function test_notification_can_be_marked_as_read()
    {
        $this->notification->markAsRead();

        $this->assertTrue($this->notification->isRead());
        $this->assertNotNull($this->notification->read_at);
    }

    public function test_notification_can_be_marked_as_unread()
    {
        $this->notification->markAsRead();
        $this->assertTrue($this->notification->isRead());

        $this->notification->markAsUnread();
        $this->assertFalse($this->notification->isRead());
        $this->assertNull($this->notification->read_at);
    }

    public function test_marking_read_notification_as_read_again_does_nothing()
    {
        $this->notification->markAsRead();
        $firstReadTime = $this->notification->read_at;

        // Wait a moment to ensure time difference would be detectable
        sleep(1);

        $this->notification->markAsRead();
        $this->assertEquals($firstReadTime, $this->notification->read_at);
    }

    public function test_unread_scope_filters_correctly()
    {
        // Create another notification and mark it as read
        $readNotification = UserNotification::create([
            'user_id' => $this->user->id,
            'title' => 'Read Notification',
            'message' => 'This notification will be read',
            'type' => 'info',
            'sent_by' => $this->admin->id,
        ]);
        $readNotification->markAsRead();

        $unreadNotifications = UserNotification::unread()->get();

        $this->assertCount(1, $unreadNotifications);
        $this->assertEquals($this->notification->id, $unreadNotifications->first()->id);
    }

    public function test_read_scope_filters_correctly()
    {
        $this->notification->markAsRead();

        // Create another unread notification
        UserNotification::create([
            'user_id' => $this->user->id,
            'title' => 'Unread Notification',
            'message' => 'This notification will remain unread',
            'type' => 'info',
            'sent_by' => $this->admin->id,
        ]);

        $readNotifications = UserNotification::read()->get();

        $this->assertCount(1, $readNotifications);
        $this->assertEquals($this->notification->id, $readNotifications->first()->id);
    }

    public function test_type_scope_filters_correctly()
    {
        UserNotification::create([
            'user_id' => $this->user->id,
            'title' => 'Warning Notification',
            'message' => 'This is a warning',
            'type' => 'warning',
            'sent_by' => $this->admin->id,
        ]);

        $infoNotifications = UserNotification::type('info')->get();
        $warningNotifications = UserNotification::type('warning')->get();

        $this->assertCount(1, $infoNotifications);
        $this->assertCount(1, $warningNotifications);
        $this->assertEquals('info', $infoNotifications->first()->type);
        $this->assertEquals('warning', $warningNotifications->first()->type);
    }

    public function test_for_user_scope_filters_correctly()
    {
        $otherUser = User::factory()->create();
        UserNotification::create([
            'user_id' => $otherUser->id,
            'title' => 'Other User Notification',
            'message' => 'This belongs to another user',
            'type' => 'info',
            'sent_by' => $this->admin->id,
        ]);

        $userNotifications = UserNotification::forUser($this->user->id)->get();
        $otherUserNotifications = UserNotification::forUser($otherUser->id)->get();

        $this->assertCount(1, $userNotifications);
        $this->assertCount(1, $otherUserNotifications);
        $this->assertEquals($this->user->id, $userNotifications->first()->user_id);
        $this->assertEquals($otherUser->id, $otherUserNotifications->first()->user_id);
    }

    public function test_recent_scope_filters_correctly()
    {
        // Create an old notification with a specific old date
        $oldDate = now()->subDays(35);
        $oldNotification = new UserNotification([
            'user_id' => $this->user->id,
            'title' => 'Old Notification',
            'message' => 'This is an old notification',
            'type' => 'info',
            'sent_by' => $this->admin->id,
        ]);
        $oldNotification->created_at = $oldDate;
        $oldNotification->updated_at = $oldDate;
        $oldNotification->save();

        // Verify the old notification was created with the correct date
        $oldNotification->refresh();
        $this->assertTrue($oldNotification->created_at->lt(now()->subDays(30)));

        $recentNotifications = UserNotification::recent(30)->get();

        $this->assertCount(1, $recentNotifications);
        $this->assertEquals($this->notification->id, $recentNotifications->first()->id);
    }

    public function test_resolve_route_binding_returns_notification_for_owner()
    {
        $this->actingAs($this->user);

        $resolvedNotification = (new UserNotification())->resolveRouteBinding($this->notification->id);

        $this->assertNotNull($resolvedNotification);
        $this->assertEquals($this->notification->id, $resolvedNotification->id);
    }

    public function test_resolve_route_binding_returns_null_for_non_owner()
    {
        $otherUser = User::factory()->create();
        $this->actingAs($otherUser);

        $resolvedNotification = (new UserNotification())->resolveRouteBinding($this->notification->id);

        $this->assertNull($resolvedNotification);
    }

    public function test_resolve_route_binding_returns_null_for_unauthenticated_user()
    {
        $resolvedNotification = (new UserNotification())->resolveRouteBinding($this->notification->id);

        $this->assertNull($resolvedNotification);
    }

    public function test_resolve_route_binding_returns_null_for_non_existent_notification()
    {
        $this->actingAs($this->user);

        $resolvedNotification = (new UserNotification())->resolveRouteBinding(999);

        $this->assertNull($resolvedNotification);
    }

    public function test_notification_fillable_attributes()
    {
        $fillable = (new UserNotification())->getFillable();

        $expectedFillable = [
            'user_id',
            'title',
            'message',
            'type',
            'read_at',
            'sent_by',
        ];

        $this->assertEquals($expectedFillable, $fillable);
    }

    public function test_notification_casts_read_at_to_datetime()
    {
        $this->notification->markAsRead();
        $this->notification->refresh();

        $this->assertInstanceOf(\Carbon\Carbon::class, $this->notification->read_at);
    }

    public function test_user_notifications_relationship()
    {
        $userNotifications = $this->user->notifications;

        $this->assertCount(1, $userNotifications);
        $this->assertEquals($this->notification->id, $userNotifications->first()->id);
    }
}
