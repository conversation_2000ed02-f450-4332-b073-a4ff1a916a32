<?php

namespace Database\Seeders;

use App\Models\PricingPlan;
use Illuminate\Database\Seeder;

class FixFreePlanPaymentMethodsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * 
     * This seeder fixes the payment method settings for free plans that were
     * incorrectly enabled by the ActivateEnterprisePlanSeeder.
     */
    public function run(): void
    {
        $this->command->info('Fixing payment method settings for free plans...');
        
        // Find all free plans (price = 0)
        $freePlans = PricingPlan::where('price', 0)->get();
        
        if ($freePlans->isEmpty()) {
            $this->command->info('No free plans found in database.');
            return;
        }
        
        foreach ($freePlans as $plan) {
            $this->command->info("Fixing payment methods for plan: {$plan->name} (ID: {$plan->id})");
            
            // Disable all payment methods for free plans
            $plan->update([
                'online_payment_enabled' => false,
                'offline_payment_enabled' => false,
                'crypto_payment_enabled' => false,
                
                // Clear payment integration IDs since they're not needed for free plans
                'paddle_product_id' => null,
                'paddle_price_id_monthly' => null,
                'paddle_price_id_yearly' => null,
                'shurjopay_product_id' => null,
                'shurjopay_price_id_monthly' => null,
                'shurjopay_price_id_yearly' => null,
                'coinbase_commerce_product_id' => null,
                'coinbase_commerce_price_id_monthly' => null,
                'coinbase_commerce_price_id_yearly' => null,
                
                // Reset fee configurations to zero for free plans
                'paddle_fee_percentage' => 0,
                'paddle_fee_fixed' => 0,
                'shurjopay_fee_percentage' => 0,
                'shurjopay_fee_fixed' => 0,
                'coinbase_commerce_fee_percentage' => 0,
                'coinbase_commerce_fee_fixed' => 0,
                'offline_fee_percentage' => 0,
                'offline_fee_fixed' => 0,
                'tax_percentage' => 0,
                'show_fees_breakdown' => false,
                'tax_inclusive' => false,
            ]);
        }
        
        $this->command->info("Fixed payment method settings for {$freePlans->count()} free plan(s).");
        
        // Verify the fix
        $problematicPlans = PricingPlan::where('price', 0)
            ->where(function ($query) {
                $query->where('online_payment_enabled', true)
                      ->orWhere('offline_payment_enabled', true)
                      ->orWhere('crypto_payment_enabled', true);
            })
            ->count();
            
        if ($problematicPlans > 0) {
            $this->command->warn("Warning: {$problematicPlans} free plan(s) still have payment methods enabled.");
        } else {
            $this->command->info('All free plans now have payment methods properly disabled.');
        }
    }
}
